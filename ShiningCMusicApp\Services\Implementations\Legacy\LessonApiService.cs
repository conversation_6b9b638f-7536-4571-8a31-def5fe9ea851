using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public class LessonApiService : ILessonApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;

        public LessonApiService(HttpClient httpClient, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    Console.WriteLine("Failed to get authentication token");
                    return new List<ScheduleEvent>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/lessons");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var lessons = JsonSerializer.Deserialize<List<ScheduleEvent>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return lessons ?? new List<ScheduleEvent>();
                }
                else
                {
                    Console.WriteLine($"Failed to get lessons: {response.StatusCode}");
                    return new List<ScheduleEvent>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting lessons: {ex.Message}");
                return new List<ScheduleEvent>();
            }
        }

        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(lesson);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{_baseUrl}/lessons", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ScheduleEvent>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating lesson: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(lesson);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"{_baseUrl}/lessons/{id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating lesson: {ex.Message}");
                return false;
            }
        }

        // Keep the old method for backward compatibility
        public async Task<bool> UpdateLessonAsync(ScheduleEvent lesson)
        {
            return await UpdateLessonAsync(lesson.Id, lesson);
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/lessons/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting lesson: {ex.Message}");
                return false;
            }
        }
    }
}
