using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface ILocationApiService
    {
        Task<List<Location>> GetLocationsAsync();
        Task<Location?> GetLocationAsync(int id);
        Task<Location?> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(int id, Location location);
        Task<bool> DeleteLocationAsync(int id);
    }
}
