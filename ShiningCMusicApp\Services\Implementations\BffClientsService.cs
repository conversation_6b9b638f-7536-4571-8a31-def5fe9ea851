using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffClientsService : IBffClientsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffClientsService> _logger;
        private readonly string _baseUrl;

        public BffClientsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffClientsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<ClientInfo>> GetClientsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching clients from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/clients");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var clients = JsonSerializer.Deserialize<List<ClientInfo>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return clients ?? new List<ClientInfo>();
                }

                _logger.LogWarning("Failed to fetch clients. Status: {StatusCode}", response.StatusCode);
                return new List<ClientInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching clients from BFF");
                return new List<ClientInfo>();
            }
        }

        public async Task<ClientInfo?> GetClientAsync(string clientId)
        {
            try
            {
                _logger.LogInformation("Fetching client {ClientId} from BFF", clientId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/clients/{Uri.EscapeDataString(clientId)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ClientInfo>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch client {ClientId}. Status: {StatusCode}", clientId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching client {ClientId} from BFF", clientId);
                return null;
            }
        }

        public async Task<ClientCreateResult> CreateClientAsync(CreateClientRequest request)
        {
            try
            {
                _logger.LogInformation("Creating client {ClientId} via BFF", request.ClientId);
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/clients");
                httpRequest.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(request);
                httpRequest.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(httpRequest);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ClientCreateResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new ClientCreateResult { Message = "Unknown result", ClientId = request.ClientId, TokenLifetimeSeconds = request.TokenLifetimeSeconds };
                }

                _logger.LogWarning("Failed to create client {ClientId}. Status: {StatusCode}", request.ClientId, response.StatusCode);
                return new ClientCreateResult { Message = $"Failed to create client. Status: {response.StatusCode}", ClientId = request.ClientId, TokenLifetimeSeconds = request.TokenLifetimeSeconds };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client {ClientId} via BFF", request.ClientId);
                return new ClientCreateResult { Message = $"Error: {ex.Message}", ClientId = request.ClientId, TokenLifetimeSeconds = request.TokenLifetimeSeconds };
            }
        }

        public async Task<bool> DeleteClientAsync(string clientId)
        {
            try
            {
                _logger.LogInformation("Deleting client {ClientId} via BFF", clientId);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/clients/{Uri.EscapeDataString(clientId)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Client {ClientId} deleted successfully", clientId);
                    return true;
                }

                _logger.LogWarning("Failed to delete client {ClientId}. Status: {StatusCode}", clientId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting client {ClientId} via BFF", clientId);
                return false;
            }
        }
    }
}
