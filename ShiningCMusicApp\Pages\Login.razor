@page "/login"
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Components
@using ShiningCMusicCommon.Enums
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using System.ComponentModel.DataAnnotations
@inherits LoginBase

<PageTitle>Login - Shining C Music Studio</PageTitle>

@if (isLoggingIn)
{
    <ThemeLoader LoadingMessage="Signing you in...">
        <div></div>
    </ThemeLoader>
}
else
{
    <div class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <img src="/logo.png" class="logo-login" />
    @*             <h1 class="display-6 d-none d-sm-block">🎵 Shining C Music Studio</h1>
                <h2 class="d-sm-none">🎵 Shining C Music</h2> *@
                <p class="text-muted">Please sign in to continue</p>
            </div>

            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                <DataAnnotationsValidator />

                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="loginModel.LoginName"
                              Placeholder="Enter your login name"
                              CssClass="form-control"
                              Enabled="@(!isLoggingIn)"></SfTextBox>
                    <ValidationMessage For="@(() => loginModel.LoginName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <SfTextBox @bind-Value="loginModel.Password"
                              Type="InputType.Password"
                              Placeholder="Enter your password"
                              CssClass="form-control"
                              Enabled="@(!isLoggingIn)"></SfTextBox>
                    <ValidationMessage For="@(() => loginModel.Password)" />
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> @errorMessage
                    </div>
                }

                <div class="d-grid">
                    <SfButton CssClass="btn btn-blue-custom btn-lg"
                             type="submit"
                             Disabled="@isLoggingIn">
                        @if (isLoggingIn)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            <span>Signing in...</span>
                        }
                        else
                        {
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            <span>Sign In</span>
                        }
                    </SfButton>
                </div>

            </EditForm>

    @*         <div class="mt-4 text-center">
                <small class="text-muted">
                    <strong>Demo Accounts:</strong><br>
                    Admin: admin / admin<br>
                    Tutor: sue / password123<br>
                    Student: (contact admin)
                </small>
            </div> *@
        </div>
    </div>
}
