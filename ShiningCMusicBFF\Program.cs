using Microsoft.AspNetCore.Authentication.Cookies;
using ShiningCMusicBFF.Configuration;
using ShiningCMusicBFF.Services;

var builder = WebApplication.CreateBuilder(args);

// Configure to use renamed appsettings files to avoid publish conflicts
builder.Configuration.Sources.Clear();
builder.Configuration
    .AddJsonFile("bff.appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"bff.appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddCommandLine(args);

// Add services to the container.
builder.Services.AddControllers();

// Configure CORS for Blazor WebAssembly
var allowedOrigins = new List<string>();
var envOrigins = Environment.GetEnvironmentVariable("CORS_ALLOWED_ORIGINS");
if (!string.IsNullOrEmpty(envOrigins))
{
    allowedOrigins.AddRange(envOrigins.Split(',', StringSplitOptions.RemoveEmptyEntries)
        .Select(origin => origin.Trim()));
}
else
{
    // Fallback to configuration file
    var configOrigins = builder.Configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>();
    if (configOrigins != null)
    {
        allowedOrigins.AddRange(configOrigins);
    }
    else
    {
        // Development defaults
        allowedOrigins.AddRange(new[] { "https://localhost:5143", "http://localhost:5143" });
    }
}

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins(allowedOrigins.ToArray())
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// Configure Authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/bff/auth/login";
        options.LogoutPath = "/bff/auth/logout";
        options.AccessDeniedPath = "/bff/auth/access-denied";
        options.Cookie.Name = "ShiningCMusicBFF";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        options.Cookie.SameSite = SameSiteMode.Lax;
        options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
        options.SlidingExpiration = true;
    });

builder.Services.AddAuthorization();

// Configure API settings - check environment variables first
var apiConfig = new ApiConfiguration
{
    BaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL") ??
              builder.Configuration["ApiSettings:BaseUrl"] ??
              "https://localhost:7268",
    ClientId = Environment.GetEnvironmentVariable("API_CLIENT_ID") ??
               builder.Configuration["ApiSettings:ClientId"] ??
               "bff_client",
    ClientSecret = Environment.GetEnvironmentVariable("API_CLIENT_SECRET") ??
                   builder.Configuration["ApiSettings:ClientSecret"] ??
                   throw new InvalidOperationException("BFF client secret not configured")
};
builder.Services.AddSingleton(apiConfig);

// Configure HttpClient for API calls
var httpClientName = "ApiClient";
builder.Services.AddHttpClient(httpClientName, client =>
{
    client.BaseAddress = new Uri(apiConfig.BaseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "ShiningCMusicBFF/1.0");
});

// Register TokenCacheService as Singleton to maintain token cache across requests
builder.Services.AddSingleton<ITokenCacheService, TokenCacheService>();

// Register ApiClientService as Scoped - token caching is handled by TokenCacheService
builder.Services.AddScoped<ApiClientService>(serviceProvider =>
{
    var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient(httpClientName);
    var tokenCache = serviceProvider.GetRequiredService<ITokenCacheService>();
    return new ApiClientService(httpClient, apiConfig, tokenCache);
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "Shining C Music BFF API", Version = "v1" });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Shining C Music BFF API v1");
    });
}

app.UseHttpsRedirection();

// Configure Blazor WebAssembly hosting
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Add fallback routing for Blazor WebAssembly
app.MapFallbackToFile("index.html");

app.Run();
