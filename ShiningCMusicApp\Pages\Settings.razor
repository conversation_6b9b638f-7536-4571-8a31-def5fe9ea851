@page "/settings"
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns

@attribute [Authorize(Roles = nameof(UserRoleEnum.Administrator))]

<PageTitle>Settings - ShiningC Music</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Settings</h2>
            <p class="text-muted">Manage application configuration settings</p>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading settings...</p>
        </div>
    }
    else if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Configuration Error</h4>
                    <p>@errorMessage</p>
                    <hr>
                    <p class="mb-0">Please ensure that:</p>
                    <ul>
                        <li>The database is running and accessible</li>
                        <li>The Config table has been created (run the SQL migration script)</li>
                        <li>The API service is running and responding correctly</li>
                    </ul>
                </div>
            </div>
        </div>
    }
    else if (configGroups?.Any() == true)
    {
        <div class="row">
            <div class="col-12">
                <SfTab @bind-SelectedItem="selectedTabIndex">
                    <TabItems>
                        @foreach (var group in configGroups.OrderBy(g => g.GroupId))
                        {
                            <TabItem>
                                <ChildContent>
                                    <TabHeader Text="@group.DisplayName" />
                                </ChildContent>
                                <ContentTemplate>
                                    <div class="p-3">
                                        @if (group.GroupId == (int)ConfigGroupId.BackgroundProcessors)
                                        {
                                            <div class="mb-3">
                                                <h5>Background Processors</h5>
                                                <p class="text-muted">Enable or disable background processing services</p>
                                                @RenderBackgroundProcessorToggles(group)
                                            </div>
                                        }
                                        @RenderConfigGroup(group)
                                    </div>
                                </ContentTemplate>
                            </TabItem>
                        }
                    </TabItems>
                </SfTab>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                    <button class="btn btn-primary" style="min-width: 200px;" @onclick="SaveChangesAsync" disabled="@(!hasChanges)">
                        <i class="bi bi-check-circle" style="color: white;"></i>
                        <span class="d-none d-sm-inline ms-2">Save Changes</span>
                        <span class="d-sm-none ms-2">Save</span>
                    </button>
                    <button class="btn btn-secondary" style="min-width: 200px;" @onclick="LoadConfigurationsAsync">
                        <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                        <span class="d-none d-sm-inline ms-2">Refresh</span>
                        <span class="d-sm-none ms-2">Refresh</span>
                    </button>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning">
                    <SfIcon Name="IconName.Warning" />
                    No configuration settings found.
                </div>
            </div>
        </div>
    }
</div>

@code {
    private RenderFragment RenderBackgroundProcessorToggles(ConfigGroup group)
    {
        return @<div class="row">
            @foreach (var processor in GetBackgroundProcessors())
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">@GetProcessorDisplayName(processor)</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       checked="@backgroundProcessorStates[processor]"
                                       @onchange="@((e) => OnBackgroundProcessorToggle(processor, (bool)e.Value!))" />
                            </div>
                            <small class="text-muted d-block mt-1">
                                @GetProcessorDescription(processor)
                            </small>
                        </div>
                    </div>
                </div>
            }
        </div>;
    }

    private RenderFragment RenderConfigGroup(ConfigGroup group)
    {
        var nonProcessorConfigs = group.Configs.Where(c =>
            (group.GroupId != (int)ConfigGroupId.BackgroundProcessors || !c.Key.EndsWith("Enabled")) &&
            c.Visible).ToList();

        if (!nonProcessorConfigs.Any())
            return @<div></div>;

        return @<div>
            <h5 class="mt-4">@group.DisplayName Configuration</h5>
            <div class="row">
                @foreach (var config in nonProcessorConfigs)
                {
                    <div class="col-12 col-sm-6 col-md-4 mb-3">
                        <label class="form-label">@config.Key</label>
                        @if (!string.IsNullOrEmpty(config.Description))
                        {
                            <small class="text-muted d-block">@config.Description</small>
                        }
                        @if (config.Key == "SidebarTheme")
                        {
                            <SfDropDownList TValue="int" TItem="SidebarThemeOption"
                                          DataSource="@sidebarThemeOptions"
                                          Value="@GetIntValue(config.ConfigId)"
                                          ValueChanged="@((int value) => SetIntValue(config.ConfigId, value))"
                                          CssClass="form-control">
                                <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                                <DropDownListTemplates TItem="SidebarThemeOption">
                                    <ItemTemplate>
                                        <div class="d-flex align-items-center">
                                            <div class="sidebar-theme-preview me-2" style="background: @context.PreviewColor; width: 20px; height: 20px; border-radius: 3px;"></div>
                                            <span>@context.Text</span>
                                        </div>
                                    </ItemTemplate>
                                    <ValueTemplate>
                                        <div class="d-flex align-items-center">
                                            <div class="sidebar-theme-preview me-2" style="background: @context.PreviewColor; width: 20px; height: 20px; border-radius: 3px;"></div>
                                            <span>@context.Text</span>
                                        </div>
                                    </ValueTemplate>
                                </DropDownListTemplates>
                            </SfDropDownList>

                            @* Show custom color pickers when Custom theme is selected *@
                            @if (GetIntValue(config.ConfigId) == (int)SidebarTheme.Custom)
                            {
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Primary Color</label>
                                            <input type="color" class="form-control form-control-color"
                                                   value="@GetCustomColor1Value()"
                                                   @onchange="@((e) => OnCustomColor1Change(e.Value?.ToString() ?? "#6c5ce7"))" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Secondary Color</label>
                                            <input type="color" class="form-control form-control-color"
                                                   value="@GetCustomColor2Value()"
                                                   @onchange="@((e) => OnCustomColor2Change(e.Value?.ToString() ?? "#a29bfe"))" />
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else if (config.DataType?.ToLower() == "bool")
                        {
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       checked="@GetBoolValue(config.ConfigId)"
                                       @onchange="@((e) => SetBoolValue(config.ConfigId, (bool)e.Value!))" />
                            </div>
                        }
                        else if (config.DataType?.ToLower() == "int")
                        {
                            <input type="number" class="form-control"
                                   value="@GetIntValue(config.ConfigId)"
                                   @onchange="@((e) => SetIntValue(config.ConfigId, int.Parse(e.Value?.ToString() ?? "0")))" />
                        }
                        else
                        {
                            <input type="text" class="form-control"
                                   value="@configValues[config.ConfigId]"
                                   @onchange="@((e) => OnConfigValueChange(config.ConfigId, e.Value?.ToString() ?? ""))" />
                        }

                    </div>
                }
            </div>
        </div>;
    }
}


