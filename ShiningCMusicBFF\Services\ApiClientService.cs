using ShiningCMusicBFF.Configuration;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicBFF.Services
{
    public class ApiClientService
    {
        private readonly HttpClient _httpClient;
        private readonly ApiConfiguration _apiConfig;
        private readonly ITokenCacheService _tokenCache;

        public ApiClientService(HttpClient httpClient, ApiConfiguration apiConfig, ITokenCacheService tokenCache)
        {
            _httpClient = httpClient;
            _apiConfig = apiConfig;
            _tokenCache = tokenCache;
        }

        public async Task<HttpResponseMessage> GetAsync(string endpoint)
        {
            await EnsureAuthenticatedAsync();
            var apiEndpoint = BuildApiEndpoint(endpoint);
            return await _httpClient.GetAsync(apiEndpoint);
        }

        public async Task<HttpResponseMessage> PostAsync(string endpoint, object? data = null)
        {
            await EnsureAuthenticatedAsync();
            var apiEndpoint = BuildApiEndpoint(endpoint);

            HttpContent? content = null;
            if (data != null)
            {
                var json = JsonSerializer.Serialize(data);
                content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            return await _httpClient.PostAsync(apiEndpoint, content);
        }

        public async Task<HttpResponseMessage> PutAsync(string endpoint, object? data)
        {
            await EnsureAuthenticatedAsync();
            var apiEndpoint = BuildApiEndpoint(endpoint);

            var json = JsonSerializer.Serialize(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            return await _httpClient.PutAsync(apiEndpoint, content);
        }

        public async Task<HttpResponseMessage> DeleteAsync(string endpoint)
        {
            await EnsureAuthenticatedAsync();
            var apiEndpoint = BuildApiEndpoint(endpoint);
            return await _httpClient.DeleteAsync(apiEndpoint);
        }

        public async Task<HttpResponseMessage> PatchAsync(string endpoint, object data)
        {
            await EnsureAuthenticatedAsync();
            var apiEndpoint = BuildApiEndpoint(endpoint);
            var json = JsonSerializer.Serialize(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            return await _httpClient.PatchAsync(apiEndpoint, content);
        }

        private string BuildApiEndpoint(string endpoint)
        {
            // If endpoint already starts with /api/, use as-is
            if (endpoint.StartsWith(_apiConfig.ApiPrefix + "/"))
                return endpoint;

            // Otherwise, prepend /api/ to the endpoint
            return $"{_apiConfig.ApiPrefix}/{endpoint.TrimStart('/')}";
        }

        private async Task EnsureAuthenticatedAsync()
        {
            var token = await _tokenCache.GetValidTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                throw new InvalidOperationException("Failed to obtain valid authentication token");
            }

            // Ensure the Authorization header is set for API requests
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }



        public async Task<T?> GetJsonAsync<T>(string endpoint)
        {
            var response = await GetAsync(endpoint);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            return default;
        }

        public async Task<bool> PostJsonAsync(string endpoint, object data)
        {
            var response = await PostAsync(endpoint, data);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> PutJsonAsync(string endpoint, object data)
        {
            var response = await PutAsync(endpoint, data);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> DeleteJsonAsync(string endpoint)
        {
            var response = await DeleteAsync(endpoint);
            return response.IsSuccessStatusCode;
        }
    }


}
