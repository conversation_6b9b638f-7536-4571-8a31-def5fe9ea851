{"description": "Azure App Service Environment Variables for Option 1 (Same Service)", "instructions": "Copy these to Azure Portal → App Service → Configuration → Application Settings", "environment_variables": {"database": {"CONNECTIONSTRINGS__DEFAULTCONNECTION": {"value": "Server=your-server.database.windows.net;Database=ShiningCMusicDB;User Id=your-username;Password=your-password;Encrypt=True;TrustServerCertificate=False;", "description": "SQL Server connection string"}}, "openiddict": {"OPENIDDICT__CLIENT_ID": {"value": "shining-c-music-client", "description": "OpenIddict client identifier"}, "OPENIDDICT__CLIENT_SECRET": {"value": "your-secure-client-secret-here", "description": "OpenIddict client secret (generate a strong random string)"}}, "email": {"EMAIL__SMTP_HOST": {"value": "smtp.gmail.com", "description": "SMTP server hostname"}, "EMAIL__SMTP_PORT": {"value": "587", "description": "SMTP server port"}, "EMAIL__SMTP_USERNAME": {"value": "<EMAIL>", "description": "SMTP username (usually your email)"}, "EMAIL__SMTP_PASSWORD": {"value": "your-app-password", "description": "SMTP password (use app password for Gmail)"}, "EMAIL__FROM_EMAIL": {"value": "<EMAIL>", "description": "Default from email address"}, "EMAIL__FROM_NAME": {"value": "Shining C Music", "description": "Default from name"}}, "cors": {"CORS_ALLOWED_ORIGINS": {"value": "https://your-app-name.azurewebsites.net", "description": "Allowed CORS origins (replace with your actual domain)"}}, "syncfusion": {"SYNCFUSION_LICENSE": {"value": "your-syncfusion-license-key", "description": "Syncfusion license key"}}, "session": {"SESSION_TIMEOUT_MINUTES": {"value": "30", "description": "Session timeout in minutes"}, "SHOW_ACTION_BUTTON_LABEL": {"value": "true", "description": "Whether to show action button labels"}}, "bff_configuration": {"API_BASE_URL": {"value": "https://your-app-name.azurewebsites.net/api", "description": "Base URL for BFF to call API (same service)"}, "BFF_CLIENT_ID": {"value": "bff-client", "description": "Client ID for BFF to authenticate with API"}, "BFF_CLIENT_SECRET": {"value": "your-bff-client-secret", "description": "Client secret for BFF authentication"}}}, "azure_cli_commands": {"description": "Use these Azure CLI commands to set environment variables", "commands": ["# Set database connection", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings CONNECTIONSTRINGS__DEFAULTCONNECTION=\"Server=your-server.database.windows.net;Database=ShiningCMusicDB;User Id=your-username;Password=your-password;Encrypt=True;TrustServerCertificate=False;\"", "", "# Set OpenIddict configuration", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings OPENIDDICT__CLIENT_ID=\"shining-c-music-client\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings OPENIDDICT__CLIENT_SECRET=\"your-secure-client-secret-here\"", "", "# Set email configuration", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__SMTP_HOST=\"smtp.gmail.com\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__SMTP_PORT=\"587\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__SMTP_USERNAME=\"<EMAIL>\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__SMTP_PASSWORD=\"your-app-password\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__FROM_EMAIL=\"<EMAIL>\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings EMAIL__FROM_NAME=\"Shining C Music\"", "", "# Set CORS configuration", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings CORS_ALLOWED_ORIGINS=\"https://your-app-name.azurewebsites.net\"", "", "# Set Syncfusion license", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings SYNCFUSION_LICENSE=\"your-syncfusion-license-key\"", "", "# Set session configuration", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings SESSION_TIMEOUT_MINUTES=\"30\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings SHOW_ACTION_BUTTON_LABEL=\"true\"", "", "# Set BFF configuration", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings API_BASE_URL=\"https://your-app-name.azurewebsites.net/api\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings BFF_CLIENT_ID=\"bff-client\"", "az webapp config appsettings set --resource-group YOUR_RESOURCE_GROUP --name YOUR_APP_NAME --settings BFF_CLIENT_SECRET=\"your-bff-client-secret\""]}, "security_notes": {"client_secrets": "Generate strong random strings for OPENIDDICT__CLIENT_SECRET and BFF_CLIENT_SECRET", "email_password": "Use app passwords for Gmail, not your regular password", "connection_string": "Use Azure SQL Database with proper firewall rules", "environment_specific": "Use different secrets for development, staging, and production"}}