using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffConfigService
    {
        // Config Groups
        Task<List<ConfigGroup>> GetConfigGroupsAsync();
        Task<ConfigGroup?> GetConfigGroupAsync(int groupId);

        // Configs
        Task<List<Config>> GetAllConfigsAsync();
        Task<Config?> GetConfigAsync(int groupId, string key);
        Task<Config?> CreateConfigAsync(Config config);
        Task<bool> UpdateConfigAsync(int configId, ConfigUpdateRequest request);
        Task<bool> UpdateConfigsAsync(List<ConfigUpdateRequest> requests);
        Task<bool> DeleteConfigAsync(int configId);

        // Background Processors
        Task<bool> IsBackgroundProcessorEnabledAsync(string processorName);
    }


}
