using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffSubjectsService
    {
        // CRUD Operations
        Task<List<Subject>> GetSubjectsAsync();
        Task<Subject?> GetSubjectAsync(int id);
        Task<Subject?> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(int id, Subject subject);
        Task<bool> DeleteSubjectAsync(int id);
    }
}
