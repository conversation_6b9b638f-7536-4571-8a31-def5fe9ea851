using Microsoft.AspNetCore.Components.Authorization;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using System.Security.Claims;

namespace ShiningCMusicApp.Services
{
    public class BffAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IWebBffAuthenticationService _bffAuthService;
        private ISessionTimeoutService? _sessionTimeoutService;

        public BffAuthenticationStateProvider(IWebBffAuthenticationService bffAuthService)
        {
            _bffAuthService = bffAuthService;
        }

        public void SetSessionTimeoutService(ISessionTimeoutService sessionTimeoutService)
        {
            _sessionTimeoutService = sessionTimeoutService;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                Console.WriteLine("BffAuthenticationStateProvider: Checking authentication state...");
                var isAuthenticated = await _bffAuthService.IsAuthenticatedAsync();
                Console.WriteLine($"BffAuthenticationStateProvider: IsAuthenticated = {isAuthenticated}");

                if (!isAuthenticated)
                {
                    Console.WriteLine("BffAuthenticationStateProvider: User not authenticated, returning empty identity");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var user = await _bffAuthService.GetCurrentUserAsync();
                Console.WriteLine($"BffAuthenticationStateProvider: Current user = {user?.LoginName}");
                if (user == null)
                {
                    Console.WriteLine("BffAuthenticationStateProvider: User is null, returning empty identity");
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, user.UserName ?? user.LoginName),
                    new Claim(ClaimTypes.NameIdentifier, user.LoginName),
                    new Claim("UserId", user.LoginName),
                    new Claim("RoleId", user.RoleId?.ToString() ?? "0")
                };

                // Add role claim based on RoleId
                var roleName = user.RoleId switch
                {
                    (int)UserRoleEnum.Administrator => UserRoleEnum.Administrator.ToString(),
                    (int)UserRoleEnum.Tutor => UserRoleEnum.Tutor.ToString(),
                    (int)UserRoleEnum.Student => UserRoleEnum.Student.ToString(),
                    _ => "Unknown"
                };

                claims.Add(new Claim(ClaimTypes.Role, roleName));

                var identity = new ClaimsIdentity(claims, "BffAuthentication");
                var principal = new ClaimsPrincipal(identity);

                Console.WriteLine($"BffAuthenticationStateProvider: Returning authenticated state for {user.LoginName}");
                return new AuthenticationState(principal);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BffAuthenticationStateProvider: Exception in GetAuthenticationStateAsync: {ex.Message}");
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        public async Task<bool> LoginAsync(string loginName, string password)
        {
            var success = await _bffAuthService.LoginAsync(loginName, password);
            
            if (success)
            {
                NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
                
                // Start session timeout monitoring
                if (_sessionTimeoutService != null)
                {
                    await _sessionTimeoutService.StartMonitoringAsync();
                }
            }
            
            return success;
        }

        public async Task LogoutAsync()
        {
            await _bffAuthService.LogoutAsync();
            
            // Stop session timeout monitoring
            if (_sessionTimeoutService != null)
            {
                await _sessionTimeoutService.StopMonitoringAsync();
            }
            
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            return await _bffAuthService.GetCurrentUserAsync();
        }

        public void NotifyUserAuthentication()
        {
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }
    }
}
