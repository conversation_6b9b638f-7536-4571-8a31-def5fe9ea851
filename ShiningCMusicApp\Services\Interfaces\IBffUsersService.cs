using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffUsersService
    {
        // CRUD Operations
        Task<List<User>> GetUsersAsync();
        Task<User?> GetUserAsync(string loginName);
        Task<User?> <PERSON><PERSON>UserAsync(User user);
        Task<bool> UpdateUserAsync(string loginName, User user);
        Task<bool> DeleteUserAsync(string loginName);

        // User Role Management
        Task<List<UserRole>> GetUserRolesAsync();
        Task<UserRole?> GetUserRoleAsync(int id);
        Task<UserRole?> CreateUserRoleAsync(UserRole userRole);
        Task<bool> UpdateUserRoleAsync(int id, UserRole userRole);
        Task<bool> DeleteUserRoleAsync(int id);
    }
}
