using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffSubjectsService : IBffSubjectsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffSubjectsService> _logger;
        private readonly string _baseUrl;

        public BffSubjectsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffSubjectsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<Subject>> GetSubjectsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching subjects from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/subjects");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var subjects = JsonSerializer.Deserialize<List<Subject>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return subjects ?? new List<Subject>();
                }

                _logger.LogWarning("Failed to fetch subjects. Status: {StatusCode}", response.StatusCode);
                return new List<Subject>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching subjects from BFF");
                return new List<Subject>();
            }
        }

        public async Task<Subject?> GetSubjectAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching subject {SubjectId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/subjects/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Subject>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch subject {SubjectId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching subject {SubjectId} from BFF", id);
                return null;
            }
        }

        public async Task<Subject?> CreateSubjectAsync(Subject subject)
        {
            try
            {
                _logger.LogInformation("Creating subject via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/subjects");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(subject);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Subject>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create subject. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subject via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateSubjectAsync(int id, Subject subject)
        {
            try
            {
                _logger.LogInformation("Updating subject {SubjectId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/subjects/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(subject);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Subject {SubjectId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update subject {SubjectId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subject {SubjectId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteSubjectAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting subject {SubjectId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/subjects/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Subject {SubjectId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete subject {SubjectId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subject {SubjectId} via BFF", id);
                return false;
            }
        }
    }
}
