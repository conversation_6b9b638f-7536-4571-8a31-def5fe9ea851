<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.15" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShiningCMusicApp\ShiningCMusicApp.csproj" />
    <ProjectReference Include="..\ShiningCMusicCommon\ShiningCMusicCommon.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude original appsettings files to prevent publish conflicts -->
    <Content Remove="appsettings.json" />
    <Content Remove="appsettings.Development.json" />
  </ItemGroup>

</Project>
