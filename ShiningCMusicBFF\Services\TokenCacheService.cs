using System.Text.Json;
using System.Net.Http.Headers;
using ShiningCMusicBFF.Configuration;

namespace ShiningCMusicBFF.Services
{
    public interface ITokenCacheService
    {
        Task<string?> GetValidTokenAsync();
    }

    public class TokenCacheService : ITokenCacheService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TokenCacheService> _logger;
        private readonly ApiConfiguration _apiConfig;
        private string? _cachedToken;
        private DateTime _tokenExpiry = DateTime.MinValue;
        private readonly SemaphoreSlim _semaphore = new(1, 1);

        public TokenCacheService(IHttpClientFactory httpClientFactory, ILogger<TokenCacheService> logger, ApiConfiguration apiConfig)
        {
            _httpClient = httpClientFactory.CreateClient("ApiClient");
            _logger = logger;
            _apiConfig = apiConfig;
        }

        public async Task<string?> GetValidTokenAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                if (string.IsNullOrEmpty(_cachedToken) || DateTime.UtcNow >= _tokenExpiry)
                {
                    await AuthenticateAsync();
                }
                return _cachedToken;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task AuthenticateAsync()
        {
            try
            {
                _logger.LogInformation("Authenticating with API using client credentials");

                var tokenRequest = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", _apiConfig.ClientId),
                    new KeyValuePair<string, string>("client_secret", _apiConfig.ClientSecret),
                    new KeyValuePair<string, string>("scope", "ShiningCMusicApi")
                });

                // Temporarily remove authorization header for token request
                _httpClient.DefaultRequestHeaders.Authorization = null;

                // Token endpoint is at root level, not under /api
                var tokenResponse = await _httpClient.PostAsync(_apiConfig.TokenEndpoint, tokenRequest);
                
                if (tokenResponse.IsSuccessStatusCode)
                {
                    var tokenContent = await tokenResponse.Content.ReadAsStringAsync();
                    var tokenData = JsonSerializer.Deserialize<TokenResponse>(tokenContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (tokenData?.access_token != null)
                    {
                        _cachedToken = tokenData.access_token;
                        _tokenExpiry = DateTime.UtcNow.AddSeconds(tokenData.expires_in - 60); // Refresh 1 minute early
                        
                        _logger.LogInformation("Successfully authenticated with API. Token expires at: {TokenExpiry}", _tokenExpiry);
                    }
                    else
                    {
                        _logger.LogError("Token response did not contain access token");
                        throw new InvalidOperationException("Failed to obtain access token");
                    }
                }
                else
                {
                    var errorContent = await tokenResponse.Content.ReadAsStringAsync();
                    _logger.LogError("Authentication failed: {StatusCode} - {Content}", tokenResponse.StatusCode, errorContent);
                    throw new InvalidOperationException($"Authentication failed: {tokenResponse.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication");
                throw;
            }
        }

        private class TokenResponse
        {
            public string access_token { get; set; } = string.Empty;
            public string token_type { get; set; } = string.Empty;
            public int expires_in { get; set; }
            public string scope { get; set; } = string.Empty;
        }
    }
}
