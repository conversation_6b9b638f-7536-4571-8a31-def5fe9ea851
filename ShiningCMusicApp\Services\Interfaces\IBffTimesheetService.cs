using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffTimesheetService
    {
        Task<TimesheetData?> GetTimesheetDataAsync();
        Task<List<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId);
        Task<bool> CreateTimesheetAsync(Timesheet timesheet);
        Task<bool> UpdateTimesheetAsync(Timesheet timesheet);
        Task<bool> DeleteTimesheetAsync(int timesheetId);

        // Timesheet Entry Management
        Task<TimesheetEntry?> CreateTimesheetEntryAsync(TimesheetEntry entry);
        Task<bool> UpdateTimesheetEntryAsync(TimesheetEntry entry);
        Task<bool> DeleteTimesheetEntryAsync(int entryId);
        Task<List<TimesheetEntry>> CreateMultipleTimesheetEntriesAsync(int timesheetId, int numberOfEntries);
    }

    public class TimesheetData
    {
        public List<Timesheet> Timesheets { get; set; } = new();
        public List<Student> Students { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
    }
}
