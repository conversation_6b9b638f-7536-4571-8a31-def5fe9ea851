using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffDashboardService : IBffDashboardService
    {
        private readonly IBffLessonsService _lessonsService;
        private readonly IBffStudentsService _studentsService;
        private readonly IBffTutorsService _tutorsService;
        private readonly IBffSubjectsService _subjectsService;
        private readonly IBffLocationsService _locationsService;
        private readonly ILogger<BffDashboardService> _logger;

        public BffDashboardService(
            IBffLessonsService lessonsService,
            IBffStudentsService studentsService,
            IBffTutorsService tutorsService,
            IBffSubjectsService subjectsService,
            IBffLocationsService locationsService,
            ILogger<BffDashboardService> logger)
        {
            _lessonsService = lessonsService;
            _studentsService = studentsService;
            _tutorsService = tutorsService;
            _subjectsService = subjectsService;
            _locationsService = locationsService;
            _logger = logger;
        }

        public async Task<DashboardData?> GetDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching dashboard data from individual BFF services");

                // Fetch data from individual services in parallel
                var lessonsTask = _lessonsService.GetLessonsAsync();
                var studentsTask = _studentsService.GetStudentsAsync();
                var tutorsTask = _tutorsService.GetTutorsAsync();
                var subjectsTask = _subjectsService.GetSubjectsAsync();
                var locationsTask = _locationsService.GetLocationsAsync();

                await Task.WhenAll(lessonsTask, studentsTask, tutorsTask, subjectsTask, locationsTask);

                var lessons = await lessonsTask;
                var students = await studentsTask;
                var tutors = await tutorsTask;
                var subjects = await subjectsTask;
                var locations = await locationsTask;

                // Calculate stats
                var now = DateTime.Now;
                var today = now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var weekEnd = weekStart.AddDays(7);

                var stats = new DashboardStats
                {
                    TotalLessons = lessons.Count,
                    TotalStudents = students.Count,
                    TotalTutors = tutors.Count,
                    TotalSubjects = subjects.Count,
                    UpcomingLessons = lessons.Count(l => l.StartTime > now),
                    TodayLessons = lessons.Count(l => l.StartTime.Date == today),
                    ThisWeekLessons = lessons.Count(l => l.StartTime.Date >= weekStart && l.StartTime.Date < weekEnd)
                };

                var dashboardData = new DashboardData
                {
                    Lessons = lessons,
                    Students = students,
                    Tutors = tutors,
                    Subjects = subjects,
                    Locations = locations,
                    Stats = stats,
                    LoadedAt = DateTime.Now
                };

                _logger.LogInformation("Dashboard data loaded successfully. Lessons: {LessonCount}, Students: {StudentCount}",
                    dashboardData.Lessons?.Count ?? 0, dashboardData.Students?.Count ?? 0);

                return dashboardData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching dashboard data from individual BFF services");
                return null;
            }
        }

        public async Task<DashboardStats?> GetQuickStatsAsync()
        {
            try
            {
                _logger.LogInformation("Calculating quick stats from individual BFF services");

                // Get lessons to calculate stats
                var lessons = await _lessonsService.GetLessonsAsync();
                var students = await _studentsService.GetStudentsAsync();
                var tutors = await _tutorsService.GetTutorsAsync();
                var subjects = await _subjectsService.GetSubjectsAsync();

                var now = DateTime.Now;
                var today = now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var weekEnd = weekStart.AddDays(7);

                var stats = new DashboardStats
                {
                    TotalLessons = lessons.Count,
                    TotalStudents = students.Count,
                    TotalTutors = tutors.Count,
                    TotalSubjects = subjects.Count,
                    UpcomingLessons = lessons.Count(l => l.StartTime > now),
                    TodayLessons = lessons.Count(l => l.StartTime.Date == today),
                    ThisWeekLessons = lessons.Count(l => l.StartTime.Date >= weekStart && l.StartTime.Date < weekEnd)
                };

                _logger.LogInformation("Quick stats calculated successfully. Total lessons: {TotalLessons}, Today: {TodayLessons}",
                    stats.TotalLessons, stats.TodayLessons);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating quick stats from individual BFF services");
                return null;
            }
        }

        // Individual data access methods for backward compatibility
        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            return await _lessonsService.GetLessonsAsync();
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            return await _studentsService.GetStudentsAsync();
        }

        public async Task<List<Tutor>> GetTutorsAsync()
        {
            return await _tutorsService.GetTutorsAsync();
        }

        public async Task<List<Subject>> GetSubjectsAsync()
        {
            return await _subjectsService.GetSubjectsAsync();
        }

        public async Task<List<Location>> GetLocationsAsync()
        {
            return await _locationsService.GetLocationsAsync();
        }

        // Lesson Management
        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            return await _lessonsService.CreateLessonAsync(lesson);
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            return await _lessonsService.UpdateLessonAsync(id, lesson);
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            return await _lessonsService.DeleteLessonAsync(id);
        }
    }
}
