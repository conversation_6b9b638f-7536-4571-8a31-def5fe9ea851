using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/subjects")]
    [Authorize]
    public class SubjectsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<SubjectsController> _logger;

        public SubjectsController(ApiClientService apiClient, ILogger<SubjectsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/subjects
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Subject>>> GetSubjects()
        {
            try
            {
                _logger.LogInformation("Fetching subjects from API");
                var subjects = await _apiClient.GetJsonAsync<List<Subject>>("subjects");
                return Ok(subjects ?? new List<Subject>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subjects from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/subjects/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Subject>> GetSubject(int id)
        {
            try
            {
                _logger.LogInformation("Fetching subject {SubjectId} from API", id);
                var subject = await _apiClient.GetJsonAsync<Subject>($"subjects/{id}");
                
                if (subject == null)
                {
                    return NotFound(new { message = "Subject not found" });
                }

                return Ok(subject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subject {SubjectId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/subjects
        [HttpPost]
        public async Task<ActionResult<Subject>> CreateSubject([FromBody] Subject subject)
        {
            try
            {
                _logger.LogInformation("Creating subject via API");
                var response = await _apiClient.PostAsync("subjects", subject);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdSubject = System.Text.Json.JsonSerializer.Deserialize<Subject>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetSubject), new { id = createdSubject?.SubjectId }, createdSubject);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create subject. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create subject", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subject via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/subjects/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSubject(int id, [FromBody] Subject subject)
        {
            try
            {
                _logger.LogInformation("Updating subject {SubjectId} via API", id);
                var response = await _apiClient.PutAsync($"subjects/{id}", subject);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Subject updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update subject {SubjectId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Subject not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update subject", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subject {SubjectId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/subjects/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSubject(int id)
        {
            try
            {
                _logger.LogInformation("Deleting subject {SubjectId} via API", id);
                var response = await _apiClient.DeleteAsync($"subjects/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Subject deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete subject {SubjectId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Subject not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete subject", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subject {SubjectId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
