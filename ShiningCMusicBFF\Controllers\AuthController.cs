using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/auth")]
    public class AuthController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<AuthController> _logger;

        public AuthController(ApiClientService apiClient, ILogger<AuthController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] BffLoginRequest request)
        {
            try
            {
                _logger.LogInformation("Login attempt for user: {LoginName}", request.LoginName);

                // Call original API to authenticate user
                var loginRequest = new
                {
                    loginName = request.LoginName,
                    password = request.Password
                };

                var response = await _apiClient.PostAsync("user/authenticate", loginRequest);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed login attempt for user: {LoginName}. Status: {StatusCode}", 
                        request.LoginName, response.StatusCode);
                    return Unauthorized(new { message = "Invalid login credentials" });
                }

                var content = await response.Content.ReadAsStringAsync();
                var user = JsonSerializer.Deserialize<User>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (user == null)
                {
                    _logger.LogWarning("Failed login attempt for user: {LoginName} - null user returned", request.LoginName);
                    return Unauthorized(new { message = "Invalid login credentials" });
                }

                // Create claims for the authenticated user
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, user.LoginName),
                    new(ClaimTypes.Name, user.LoginName),
                    new(ClaimTypes.Role, user.RoleDescription ?? "User"),
                    new("RoleId", user.RoleId?.ToString() ?? "0"),
                    new("UserName", user.UserName ?? user.LoginName)
                };

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

                // Sign in the user with cookie authentication
                await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    claimsPrincipal,
                    new AuthenticationProperties
                    {
                        IsPersistent = false, // Session cookie
                        ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8), // 8 hour session
                        IssuedUtc = DateTimeOffset.UtcNow
                    });

                _logger.LogInformation("Successful login for user: {LoginName}, Role: {Role}", user.LoginName, user.RoleDescription);

                // Return user info (no sensitive data)
                return Ok(new
                {
                    success = true,
                    user = new
                    {
                        loginName = user.LoginName,
                        userName = user.UserName,
                        roleName = user.RoleDescription,
                        roleId = user.RoleId
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {LoginName}", request.LoginName);
                return StatusCode(500, new { message = "An error occurred during login" });
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var userName = User.Identity?.Name;
                _logger.LogInformation("Logout request for user: {UserName}", userName);

                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

                _logger.LogInformation("Successful logout for user: {UserName}", userName);
                return Ok(new { success = true, message = "Logged out successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "An error occurred during logout" });
            }
        }

        [HttpGet("user")]
        [Authorize]
        public IActionResult GetCurrentUser()
        {
            try
            {
                var user = new
                {
                    loginName = User.Identity?.Name,
                    userName = User.FindFirst("UserName")?.Value,
                    roleName = User.FindFirst(ClaimTypes.Role)?.Value,
                    roleId = int.TryParse(User.FindFirst("RoleId")?.Value, out var roleId) ? roleId : 0,
                    isAuthenticated = User.Identity?.IsAuthenticated ?? false
                };

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user info");
                return StatusCode(500, new { message = "An error occurred getting user info" });
            }
        }

        [HttpGet("check")]
        [AllowAnonymous]
        public IActionResult CheckAuthentication()
        {
            return Ok(new
            {
                isAuthenticated = User.Identity?.IsAuthenticated ?? false,
                userName = User.Identity?.Name
            });
        }

        [HttpGet("test-api")]
        [AllowAnonymous]
        public async Task<IActionResult> TestApiCall()
        {
            try
            {
                _logger.LogInformation("Testing API call to verify token sharing");

                // Make a simple API call to test token sharing
                var response = await _apiClient.GetAsync("user");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Ok(new { success = true, message = "API call successful", dataLength = content.Length });
                }
                else
                {
                    _logger.LogWarning("API call failed with status: {StatusCode}", response.StatusCode);
                    return Ok(new { success = false, message = $"API call failed with status: {response.StatusCode}" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during API test call");
                return Ok(new { success = false, message = $"Error: {ex.Message}" });
            }
        }
    }

    public class BffLoginRequest
    {
        public string LoginName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}
