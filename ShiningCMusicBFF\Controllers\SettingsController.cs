using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/settings")]
    [Authorize(Roles = "Administrator")]
    public class SettingsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<SettingsController> _logger;

        public SettingsController(ApiClientService apiClient, ILogger<SettingsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpGet("data")]
        public async Task<IActionResult> GetSettingsData()
        {
            try
            {
                _logger.LogInformation("Loading settings data from API");
                
                var configGroups = await _apiClient.GetJsonAsync<List<ConfigGroup>>("config/groups");
                return Ok(new SettingsData
                {
                    ConfigGroups = configGroups ?? new List<ConfigGroup>()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading settings data from API");
                return StatusCode(500, new { message = "An error occurred loading settings data" });
            }
        }

        [HttpGet("config/{configId}")]
        public async Task<IActionResult> GetConfig(int configId)
        {
            try
            {
                _logger.LogInformation("Loading config from API: {ConfigId}", configId);
                // Note: The original API doesn't have a direct endpoint for getting config by ID
                // We would need to implement this or use the groups endpoint and filter
                // For now, we'll return a not implemented response
                await Task.Delay(100);
                return StatusCode(501, new { message = "Get config by ID not implemented - use config groups endpoint" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading config {ConfigId} from API", configId);
                return StatusCode(500, new { message = "An error occurred loading configuration" });
            }
        }

        [HttpGet("config-value/{groupId}/{key}")]
        [AllowAnonymous] // Allow theme loading for all users
        public async Task<IActionResult> GetConfigValue(int groupId, string key)
        {
            try
            {
                _logger.LogInformation("Loading config value from API: Group {GroupId}, Key {Key}", groupId, key);

                var response = await _apiClient.GetAsync($"config/{groupId}/{key}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var config = JsonSerializer.Deserialize<Config>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(config?.Value);
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound();
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to get config value. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to get configuration value" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading config value from API: Group {GroupId}, Key {Key}", groupId, key);
                return StatusCode(500, new { message = "An error occurred loading configuration value" });
            }
        }

        [HttpPut("config/{configId}")]
        public async Task<IActionResult> UpdateConfig(int configId, [FromBody] UpdateConfigRequest request)
        {
            try
            {
                _logger.LogInformation("Updating config via API: {ConfigId}", configId);
                
                // Convert BFF request to original API request format
                var apiRequest = new
                {
                    configId = configId,
                    value = request.Value
                };
                
                var response = await _apiClient.PutAsync($"config/{configId}", apiRequest);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Configuration updated successfully" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Configuration not found" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update config. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to update configuration" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating config {ConfigId} via API", configId);
                return StatusCode(500, new { message = "An error occurred updating configuration" });
            }
        }

        [HttpPost("config/batch-update")]
        public async Task<IActionResult> BatchUpdateConfigs([FromBody] BatchUpdateConfigRequest request)
        {
            try
            {
                _logger.LogInformation("Batch updating {Count} configs via API", request.Updates.Count);
                
                // Convert BFF request to original API request format
                var apiRequests = request.Updates.Select(u => new
                {
                    configId = u.ConfigId,
                    value = u.Value
                }).ToList();
                
                var response = await _apiClient.PutAsync("config/batch", apiRequests);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    // Convert original API response to BFF format
                    var results = new List<ConfigUpdateResult>();
                    if (result.TryGetProperty("results", out var resultsArray))
                    {
                        foreach (var item in resultsArray.EnumerateArray())
                        {
                            var configId = item.TryGetProperty("configId", out var idProp) ? idProp.GetInt32() : 0;
                            var success = item.TryGetProperty("success", out var successProp) && successProp.GetBoolean();
                            
                            results.Add(new ConfigUpdateResult
                            {
                                ConfigId = configId,
                                Success = success,
                                Message = success ? "Updated successfully" : "Update failed"
                            });
                        }
                    }
                    
                    var allSuccessful = results.All(r => r.Success);
                    return Ok(new
                    {
                        success = allSuccessful,
                        message = allSuccessful ? "All configurations updated successfully" : "Some configurations failed to update",
                        results
                    });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to batch update configs. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return StatusCode(500, new { message = "An error occurred during batch configuration update" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch config update via API");
                return StatusCode(500, new { message = "An error occurred during batch configuration update" });
            }
        }

        [HttpGet("background-processors")]
        public Task<IActionResult> GetBackgroundProcessorStates()
        {
            try
            {
                // This would typically get the current state of background processors
                // For now, return a placeholder implementation
                var states = new Dictionary<string, bool>
                {
                    { "LessonCleanupService", true },
                    { "PaymentReminderService", true },
                    { "EmailService", true }
                };

                return Task.FromResult<IActionResult>(Ok(states));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading background processor states");
                return Task.FromResult<IActionResult>(StatusCode(500, new { message = "An error occurred loading background processor states" }));
            }
        }

        [HttpPost("background-processors/{processorName}/toggle")]
        public Task<IActionResult> ToggleBackgroundProcessor(string processorName, [FromBody] ToggleProcessorRequest request)
        {
            try
            {
                // This would typically enable/disable the background processor
                // For now, return a placeholder implementation
                _logger.LogInformation("Background processor {ProcessorName} {Action}",
                    processorName, request.Enabled ? "enabled" : "disabled");

                return Task.FromResult<IActionResult>(Ok(new {
                    success = true,
                    message = $"Background processor {processorName} {(request.Enabled ? "enabled" : "disabled")} successfully"
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling background processor {ProcessorName}", processorName);
                return Task.FromResult<IActionResult>(StatusCode(500, new { message = "An error occurred toggling background processor" }));
            }
        }
    }

    public class SettingsData
    {
        public List<ConfigGroup> ConfigGroups { get; set; } = new();
    }

    public class UpdateConfigRequest
    {
        public string Value { get; set; } = string.Empty;
    }

    public class BatchUpdateConfigRequest
    {
        public List<ConfigUpdate> Updates { get; set; } = new();
    }

    public class ConfigUpdate
    {
        public int ConfigId { get; set; }
        public string Value { get; set; } = string.Empty;
    }

    public class ConfigUpdateResult
    {
        public int ConfigId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ToggleProcessorRequest
    {
        public bool Enabled { get; set; }
    }
}
