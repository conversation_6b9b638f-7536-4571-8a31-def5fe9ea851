using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffLocationsService : IBffLocationsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffLocationsService> _logger;
        private readonly string _baseUrl;

        public BffLocationsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffLocationsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<Location>> GetLocationsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching locations from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/locations");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var locations = JsonSerializer.Deserialize<List<Location>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return locations ?? new List<Location>();
                }

                _logger.LogWarning("Failed to fetch locations. Status: {StatusCode}", response.StatusCode);
                return new List<Location>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching locations from BFF");
                return new List<Location>();
            }
        }

        public async Task<Location?> GetLocationAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching location {LocationId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/locations/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Location>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch location {LocationId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching location {LocationId} from BFF", id);
                return null;
            }
        }

        public async Task<Location?> CreateLocationAsync(Location location)
        {
            try
            {
                _logger.LogInformation("Creating location via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/locations");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(location);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Location>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create location. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating location via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateLocationAsync(int id, Location location)
        {
            try
            {
                _logger.LogInformation("Updating location {LocationId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/locations/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(location);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Location {LocationId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update location {LocationId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location {LocationId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteLocationAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting location {LocationId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/locations/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Location {LocationId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete location {LocationId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting location {LocationId} via BFF", id);
                return false;
            }
        }
    }
}
