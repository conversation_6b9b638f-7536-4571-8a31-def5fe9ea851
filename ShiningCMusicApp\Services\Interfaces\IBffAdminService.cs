using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffAdminService
    {
        Task<AdminData?> GetAdminDataAsync();
        
        // User Management
        Task<bool> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(string loginName);
        
        // Subject Management
        Task<bool> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(Subject subject);
        Task<bool> DeleteSubjectAsync(int subjectId);
        
        // Location Management
        Task<bool> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(Location location);
        Task<bool> DeleteLocationAsync(int locationId);
        
        // Tutor Management
        Task<bool> CreateTutorAsync(Tutor tutor);
        Task<bool> UpdateTutorAsync(Tutor tutor);
        Task<bool> DeleteTutorAsync(int tutorId);
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
        
        // Student Management
        Task<bool> CreateStudentAsync(Student student);
        Task<bool> UpdateStudentAsync(Student student);
        Task<bool> DeleteStudentAsync(int studentId);

        // User Role Management
        Task<bool> CreateUserRoleAsync(UserRole userRole);
        Task<bool> UpdateUserRoleAsync(UserRole userRole);
        Task<bool> DeleteUserRoleAsync(int roleId);
    }
}
