﻿@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Components
@inherits LayoutComponentBase

<ThemeLoader>
<div class="page">
    <!-- Simple Mobile Menu - Hidden on desktop -->
    <div class="mobile-menu d-lg-none" id="mobileMenu">
        <div class="mobile-menu-header">
            <h5><i class="bi bi-music-note me-2"></i>Shining C Music</h5>
            <button type="button" class="mobile-menu-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-menu-body">
            <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                <Authorized>
                    <a href="/" onclick="closeMobileMenu()">
                        <i class="bi bi-house-door-fill me-2"></i> Home
                    </a>
                    <a href="/lessons" onclick="closeMobileMenu()">
                        <i class="bi bi-calendar-event-fill me-2"></i> Lessons
                    </a>
                    <a href="/timesheets" onclick="closeMobileMenu()">
                        <i class="bi bi-clipboard-fill me-2"></i> Timesheets
                    </a>
                    <a href="/tutors" onclick="closeMobileMenu()">
                        <i class="bi bi-person-fill me-2"></i> Tutors
                    </a>
                    <a href="/students" onclick="closeMobileMenu()">
                        <i class="bi bi-people-fill me-2"></i> Students
                    </a>
                    <a href="/email-templates" onclick="closeMobileMenu()">
                        <i class="bi bi-envelope-fill me-2"></i> Email Templates
                    </a>
                    <a href="/admin" onclick="closeMobileMenu()">
                        <i class="bi bi-gear-fill me-2"></i> Maintenance
                    </a>
                    <a href="/settings" onclick="closeMobileMenu()">
                        <i class="bi bi-sliders-fill-mobile-menu me-2"></i> Settings
                    </a>
                </Authorized>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{UserRoleEnum.Tutor},{UserRoleEnum.Student}")">
                <Authorized>
                    <a href="/lessons" onclick="closeMobileMenu()">
                        <i class="bi bi-calendar-event-fill me-2"></i> My Lessons
                    </a>
                </Authorized>
            </AuthorizeView>
            <AuthorizeView Roles="@UserRoleEnum.Tutor.ToString()">
                <Authorized>
                    <a href="/timesheets" onclick="closeMobileMenu()">
                        <i class="bi bi-clipboard-fill me-2"></i> My Timesheets
                    </a>
                </Authorized>
            </AuthorizeView>
        </div>
    </div>

    <!-- Mobile Menu Overlay - Hidden on desktop -->
    <div class="mobile-menu-overlay d-lg-none" id="mobileMenuOverlay" onclick="closeMobileMenu()"></div>

    <!-- Desktop sidebar -->
    <div class="sidebar d-none d-lg-block">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4 d-flex align-items-center">
            <!-- Mobile menu button - left side -->
            <button class="mobile-menu-btn d-lg-none me-3" type="button" onclick="openMobileMenu()">
                <i class="bi bi-list"></i>
            </button>

            <!-- User info - right side -->
            <div class="ms-auto">
                <AuthorizeView>
                    <Authorized>
                        <div class="d-flex align-items-center">
                            <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                            <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                                <i class="bi bi-box-arrow-left"></i> Logout
                            </button>
                        </div>
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<!-- Session Timeout Initializer -->
<SessionTimeoutInitializer />
</ThemeLoader>
