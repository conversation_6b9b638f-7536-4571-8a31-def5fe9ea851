using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffTimesheetService : IBffTimesheetService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffTimesheetService> _logger;
        private readonly string _baseUrl;

        public BffTimesheetService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffTimesheetService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<TimesheetData?> GetTimesheetDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching timesheet data from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/timesheet/data");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<TimesheetData>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Timesheet data loaded successfully. Timesheets: {Count}", data?.Timesheets?.Count ?? 0);
                    return data;
                }

                _logger.LogWarning("Failed to fetch timesheet data. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching timesheet data from Web BFF");
                return null;
            }
        }

        public async Task<List<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId)
        {
            try
            {
                _logger.LogInformation("Fetching timesheet entries for timesheet {TimesheetId}", timesheetId);

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/timesheet/{timesheetId}/entries");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var entries = JsonSerializer.Deserialize<List<TimesheetEntry>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Timesheet entries loaded successfully. Count: {Count}", entries?.Count ?? 0);
                    return entries ?? new List<TimesheetEntry>();
                }

                _logger.LogWarning("Failed to fetch timesheet entries. Status: {StatusCode}", response.StatusCode);
                return new List<TimesheetEntry>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching timesheet entries for timesheet {TimesheetId}", timesheetId);
                return new List<TimesheetEntry>();
            }
        }

        public async Task<bool> CreateTimesheetAsync(Timesheet timesheet)
        {
            try
            {
                _logger.LogInformation("Creating timesheet via Web BFF");

                var json = JsonSerializer.Serialize(timesheet);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/timesheet")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Timesheet created successfully");
                    return true;
                }

                _logger.LogWarning("Failed to create timesheet. Status: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet via Web BFF");
                return false;
            }
        }

        public async Task<bool> UpdateTimesheetAsync(Timesheet timesheet)
        {
            try
            {
                _logger.LogInformation("Updating timesheet {TimesheetId} via Web BFF", timesheet.TimesheetId);

                var json = JsonSerializer.Serialize(timesheet);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/timesheet/{timesheet.TimesheetId}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Timesheet {TimesheetId} updated successfully", timesheet.TimesheetId);
                    return true;
                }

                _logger.LogWarning("Failed to update timesheet {TimesheetId}. Status: {StatusCode}", timesheet.TimesheetId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet {TimesheetId} via Web BFF", timesheet.TimesheetId);
                return false;
            }
        }

        public async Task<bool> DeleteTimesheetAsync(int timesheetId)
        {
            try
            {
                _logger.LogInformation("Deleting timesheet {TimesheetId} via Web BFF", timesheetId);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/timesheet/{timesheetId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Timesheet {TimesheetId} deleted successfully", timesheetId);
                    return true;
                }

                _logger.LogWarning("Failed to delete timesheet {TimesheetId}. Status: {StatusCode}", timesheetId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet {TimesheetId} via Web BFF", timesheetId);
                return false;
            }
        }

        public async Task<TimesheetEntry?> CreateTimesheetEntryAsync(TimesheetEntry entry)
        {
            try
            {
                _logger.LogInformation("Creating timesheet entry via Web BFF");

                var json = JsonSerializer.Serialize(entry);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/timesheet/entries")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var createdEntry = JsonSerializer.Deserialize<TimesheetEntry>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Timesheet entry created successfully");
                    return createdEntry;
                }

                _logger.LogWarning("Failed to create timesheet entry. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet entry via Web BFF");
                return null;
            }
        }

        public async Task<bool> UpdateTimesheetEntryAsync(TimesheetEntry entry)
        {
            try
            {
                _logger.LogInformation("Updating timesheet entry {EntryId} via Web BFF", entry.TimesheetEntryId);

                var json = JsonSerializer.Serialize(entry);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/timesheet/entries/{entry.TimesheetEntryId}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Timesheet entry {EntryId} updated successfully", entry.TimesheetEntryId);
                    return true;
                }

                _logger.LogWarning("Failed to update timesheet entry {EntryId}. Status: {StatusCode}", entry.TimesheetEntryId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet entry {EntryId} via Web BFF", entry.TimesheetEntryId);
                return false;
            }
        }

        public async Task<bool> DeleteTimesheetEntryAsync(int entryId)
        {
            try
            {
                _logger.LogInformation("Deleting timesheet entry {EntryId} via Web BFF", entryId);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/timesheet/entries/{entryId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Timesheet entry {EntryId} deleted successfully", entryId);
                    return true;
                }

                _logger.LogWarning("Failed to delete timesheet entry {EntryId}. Status: {StatusCode}", entryId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet entry {EntryId} via Web BFF", entryId);
                return false;
            }
        }

        public async Task<List<TimesheetEntry>> CreateMultipleTimesheetEntriesAsync(int timesheetId, int numberOfEntries)
        {
            try
            {
                _logger.LogInformation("Creating {Count} timesheet entries for timesheet {TimesheetId} via Web BFF", numberOfEntries, timesheetId);

                var requestData = new { TimesheetId = timesheetId, NumberOfEntries = numberOfEntries };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/timesheet/entries/bulk")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var entries = JsonSerializer.Deserialize<List<TimesheetEntry>>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("{Count} timesheet entries created successfully", entries?.Count ?? 0);
                    return entries ?? new List<TimesheetEntry>();
                }

                _logger.LogWarning("Failed to create multiple timesheet entries. Status: {StatusCode}", response.StatusCode);
                return new List<TimesheetEntry>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating multiple timesheet entries via Web BFF");
                return new List<TimesheetEntry>();
            }
        }
    }
}
