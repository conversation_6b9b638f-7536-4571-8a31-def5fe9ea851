using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Pages;

public partial class NotAuthorizedBase : ComponentBase
{
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected BffAuthenticationStateProvider AuthStateProvider { get; set; } = default!;

    protected void GoToHome()
    {
        Navigation.NavigateTo("/", true);
    }
    
    protected void GoToLessons()
    {
        Navigation.NavigateTo("/lessons", true);
    }
}
