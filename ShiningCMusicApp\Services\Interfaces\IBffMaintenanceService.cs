namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffMaintenanceService
    {
        // Lesson Cleanup
        Task<MaintenanceResult> CleanupAllLessonsAsync(int olderThanDays = 30);
        Task<MaintenanceCountResult> GetAllLessonsCountAsync(int olderThanDays = 30);

        // Tutor Cleanup
        Task<MaintenanceResult> CleanupArchivedTutorsAsync(int olderThanDays = 30);
        Task<MaintenanceCountResult> GetArchivedTutorsCountAsync(int olderThanDays = 30);

        // Student Cleanup
        Task<MaintenanceResult> CleanupArchivedStudentsAsync(int olderThanDays = 30);
        Task<MaintenanceCountResult> GetArchivedStudentsCountAsync(int olderThanDays = 30);
    }

    public class MaintenanceResult
    {
        public string Message { get; set; } = string.Empty;
        public int DeletedCount { get; set; }
        public int OlderThanDays { get; set; }
    }

    public class MaintenanceCountResult
    {
        public string Message { get; set; } = string.Empty;
        public int Count { get; set; }
        public int OlderThanDays { get; set; }
        public string Note { get; set; } = string.Empty;
    }
}
