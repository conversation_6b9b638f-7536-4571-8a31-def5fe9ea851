using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface IUserApiService
    {
        Task<User?> AuthenticateAsync(string loginName, string password);
        Task<List<User>> GetUsersAsync();
        Task<List<UserRole>> GetUserRolesAsync();
        Task<User?> GetUserAsync(string loginName);
        Task<User?> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(string loginName, User user);
        Task<bool> DeleteUserAsync(string loginName);
        Task<UserRole?> CreateUserRoleAsync(UserRole role);
        Task<bool> UpdateUserRoleAsync(UserRole role);
        Task<bool> DeleteUserRoleAsync(int roleId);
    }
}
