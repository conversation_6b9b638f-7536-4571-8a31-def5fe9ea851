# Deployment Configuration Guide

## Current Setup: Option 1 - Same Azure App Service

### Architecture Overview
```
Azure App Service (yourapp.azurewebsites.net)
├── Blazor WebAssembly (/)
├── API (.NET 8) (/api/*)
└── BFF (.NET 8) (/bff/*)
```

### Environment Variables for Azure App Service

Set these in Azure Portal → App Service → Configuration → Application Settings:

```bash
# Database Connection
CONNECTIONSTRINGS__DEFAULTCONNECTION="Server=your-server;Database=your-db;..."

# OpenIddict Configuration
OPENIDDICT__CLIENT_ID="your-client-id"
OPENIDDICT__CLIENT_SECRET="your-client-secret"

# Email Service Configuration
EMAIL__SMTP_HOST="smtp.gmail.com"
EMAIL__SMTP_PORT="587"
EMAIL__SMTP_USERNAME="<EMAIL>"
EMAIL__SMTP_PASSWORD="your-app-password"
EMAIL__FROM_EMAIL="<EMAIL>"
EMAIL__FROM_NAME="Shining C Music"

# CORS Configuration
CORS_ALLOWED_ORIGINS="https://yourapp.azurewebsites.net"

# Syncfusion License
SYNCFUSION_LICENSE="your-license-key"

# Session Configuration
SESSION_TIMEOUT_MINUTES="30"
SHOW_ACTION_BUTTON_LABEL="true"

# BFF API Configuration (for BFF to call API)
API_BASE_URL="https://yourapp.azurewebsites.net/api"
BFF_CLIENT_ID="bff-client"
BFF_CLIENT_SECRET="your-bff-secret"
```

### Deployment Steps

1. **Build Solution**
   ```bash
   dotnet build --configuration Release
   ```

2. **Publish API Project**
   ```bash
   dotnet publish ShiningCMusicApi -c Release -o ./publish/api
   ```

3. **Publish BFF Project**
   ```bash
   dotnet publish ShiningCMusicBFF -c Release -o ./publish/bff
   ```

4. **Publish Blazor Project**
   ```bash
   dotnet publish ShiningCMusicApp -c Release -o ./publish/blazor
   ```

5. **Combine for Deployment**
   ```bash
   # Copy API files to deployment folder
   cp -r ./publish/api/* ./deploy/
   
   # Copy BFF files to bff subfolder
   mkdir ./deploy/bff
   cp -r ./publish/bff/* ./deploy/bff/
   
   # Copy Blazor files to wwwroot
   cp -r ./publish/blazor/wwwroot/* ./deploy/wwwroot/
   ```

6. **Deploy to Azure**
   - Use Azure CLI, GitHub Actions, or Visual Studio publish

### URL Structure After Deployment
- **Blazor App**: `https://yourapp.azurewebsites.net/`
- **API Endpoints**: `https://yourapp.azurewebsites.net/api/lessons`
- **BFF Endpoints**: `https://yourapp.azurewebsites.net/bff/dashboard`

---

## Future Migration: Option 2 - Separate Services

### When to Consider Separation
- Need independent scaling of API vs BFF
- Want to deploy API and BFF on different schedules
- Performance requirements differ significantly
- Team structure supports separate service ownership

### Architecture After Separation
```
Azure App Service 1 (yourapp-api.azurewebsites.net)
└── API (.NET 8) (/api/*)

Azure App Service 2 (yourapp-bff.azurewebsites.net)  
└── BFF (.NET 8) (/bff/*)

Azure Static Web Apps (yourapp.azurestaticapps.net)
└── Blazor WebAssembly (/)
```

### Migration Steps (When Ready)

#### Step 1: Create Separate App Services
```bash
# Create resource group (if needed)
az group create --name rg-shiningcmusic --location eastus

# Create App Service Plan for API
az appservice plan create --name plan-api --resource-group rg-shiningcmusic --sku B1

# Create App Service Plan for BFF  
az appservice plan create --name plan-bff --resource-group rg-shiningcmusic --sku B1

# Create API App Service
az webapp create --name yourapp-api --resource-group rg-shiningcmusic --plan plan-api

# Create BFF App Service
az webapp create --name yourapp-bff --resource-group rg-shiningcmusic --plan plan-bff
```

#### Step 2: Update Configuration

**API App Service Environment Variables:**
```bash
CONNECTIONSTRINGS__DEFAULTCONNECTION="..."
OPENIDDICT__CLIENT_ID="..."
OPENIDDICT__CLIENT_SECRET="..."
EMAIL__SMTP_HOST="..."
# ... other API-specific config
CORS_ALLOWED_ORIGINS="https://yourapp-bff.azurewebsites.net,https://yourapp.azurestaticapps.net"
```

**BFF App Service Environment Variables:**
```bash
API_BASE_URL="https://yourapp-api.azurewebsites.net/api"
BFF_CLIENT_ID="bff-client"
BFF_CLIENT_SECRET="..."
CORS_ALLOWED_ORIGINS="https://yourapp.azurestaticapps.net"
```

#### Step 3: Update Blazor Client Configuration

Update `DetermineBffBaseUrl` and `DetermineApiBaseUrl` methods:

```csharp
static string DetermineApiBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    if (builder.HostEnvironment.BaseAddress.Contains("azurestaticapps.net"))
    {
        // Production - separate API service
        return "https://yourapp-api.azurewebsites.net/api";
    }
    
    // Local development
    return localConfig?.GetValueOrDefault("ApiBaseUrl") ?? "https://localhost:7268/api";
}

static string DetermineBffBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    if (builder.HostEnvironment.BaseAddress.Contains("azurestaticapps.net"))
    {
        // Production - separate BFF service
        return "https://yourapp-bff.azurewebsites.net/bff";
    }
    
    // Local development
    return localConfig?.GetValueOrDefault("BffBaseUrl") ?? "https://localhost:7269/bff";
}
```

#### Step 4: Deploy Separately
```bash
# Deploy API
az webapp deployment source config-zip --resource-group rg-shiningcmusic --name yourapp-api --src api-package.zip

# Deploy BFF  
az webapp deployment source config-zip --resource-group rg-shiningcmusic --name yourapp-bff --src bff-package.zip

# Deploy Blazor to Static Web Apps
az staticwebapp create --name yourapp --resource-group rg-shiningcmusic --source blazor-dist/
```

### Cost Comparison

**Option 1 (Current):**
- 1 × App Service Plan (B1): ~$13/month
- Total: ~$13/month

**Option 2 (Separated):**
- 1 × App Service Plan for API (B1): ~$13/month  
- 1 × App Service Plan for BFF (B1): ~$13/month
- 1 × Static Web Apps (Free tier): $0/month
- Total: ~$26/month

### Benefits of Each Option

| Feature | Option 1 (Same Service) | Option 2 (Separate Services) |
|---------|------------------------|------------------------------|
| Cost | Lower ($13/month) | Higher ($26/month) |
| Deployment Complexity | Simple | More Complex |
| Independent Scaling | No | Yes |
| Fault Isolation | No | Yes |
| CORS Configuration | Simple | More Complex |
| Monitoring | Single service | Per-service monitoring |
| CI/CD Complexity | Single pipeline | Multiple pipelines |

