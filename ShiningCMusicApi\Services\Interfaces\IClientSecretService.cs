using ShiningCMusicApi.Infrastructure.Entities;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IClientSecretService
    {
        Task<string?> GetPlainTextSecretAsync(string clientId);
        Task<int> GetTokenLifetimeAsync(string clientId);
        Task<ClientSecretEntity?> GetClientSecretAsync(string clientId);
        Task<ClientSecretEntity> CreateClientSecretAsync(string clientId, string plainTextSecret, string? description = null, int tokenLifetimeSeconds = 3600);
        Task<ClientSecretEntity> UpdateClientSecretAsync(string clientId, string newPlainTextSecret, int? tokenLifetimeSeconds = null);
        Task<bool> DeleteClientSecretAsync(string clientId);
        Task<bool> ValidateClientSecretAsync(string clientId, string providedSecret);
        Task<List<ClientSecretEntity>> GetAllClientSecretsAsync();
    }
}
