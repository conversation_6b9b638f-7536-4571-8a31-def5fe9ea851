using Microsoft.EntityFrameworkCore;
using ShiningCMusicApi.Infrastructure.Entities;

namespace ShiningCMusicApi.Infrastructure
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // OpenIddict plain text secrets management
        public DbSet<ClientSecretEntity> ClientSecrets { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure OpenIddict entities
            modelBuilder.UseOpenIddict();

            // Configure ClientSecrets entity
            modelBuilder.Entity<ClientSecretEntity>()
                .<PERSON><PERSON><PERSON>(cs => cs.Id);

            modelBuilder.Entity<ClientSecretEntity>()
                .HasIndex(cs => cs.ClientId)
                .IsUnique();

            modelBuilder.Entity<ClientSecretEntity>()
                .Property(cs => cs.ClientId)
                .IsRequired()
                .HasMaxLength(100);

            modelBuilder.Entity<ClientSecretEntity>()
                .Property(cs => cs.PlainTextSecret)
                .IsRequired()
                .HasMaxLength(500);
        }
    }
}
